package com.quhong.mysql.data;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

@TableName("t_robot_actor")
public class RobotActorData {
    @TableId(type = IdType.AUTO)
    private Integer id;
    private String uid;
    private Integer fbGender;
    /**
     * 1有效，0无效,2系统生成的测试账号
     */
    private Integer status;
    /**
     * 创建时间
     */
    private Long crt;

    /**
     * 生成的测试账号运营系统申请者uid
     */
    private String applicantUid;

    /**
     * 生成的测试账号使用者名字
     */
    private String opUserName;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public Integer getFbGender() {
        return fbGender;
    }

    public void setFbGender(Integer fbGender) {
        this.fbGender = fbGender;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Long getCrt() {
        return crt;
    }

    public void setCrt(Long crt) {
        this.crt = crt;
    }

    public String getApplicantUid() {
        return applicantUid;
    }

    public void setApplicantUid(String applicantUid) {
        this.applicantUid = applicantUid;
    }

    public String getOpUserName() {
        return opUserName;
    }

    public void setOpUserName(String opUserName) {
        this.opUserName = opUserName;
    }

    @Override
    public String toString() {
        return "RobotActorData{" +
                "id=" + id +
                ", uid='" + uid + '\'' +
                ", fbGender=" + fbGender +
                ", status=" + status +
                ", crt=" + crt +
                ", applicantUid='" + applicantUid + '\'' +
                ", opUserName='" + opUserName + '\'' +
                '}';
    }
}
