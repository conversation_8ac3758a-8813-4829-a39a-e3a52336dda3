package com.quhong.redis;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashSet;
import java.util.Set;

@Component
public class AreaBlackUserRedis {
    private static final Logger logger = LoggerFactory.getLogger(AreaBlackUserRedis.class);

    @Resource(name = DataRedisBean.MIC_SOURCE)
    private StringRedisTemplate redisTemplate;


    public Set<String> getAllAreaBlackUser() {
        try {
            return redisTemplate.opsForSet().members("area_black_user");
        } catch (Exception e) {
            logger.error("get all area black user error {}", e.getMessage(), e);
            return new HashSet<>();
        }
    }
}
