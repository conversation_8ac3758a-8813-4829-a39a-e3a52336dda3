package com.quhong.mysql.data;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

@TableName("t_tn_msg_cache")
public class TnMsgCacheData {
    @TableId(type = IdType.AUTO)
    private Integer id;
    private String tnMsgMd5; // tn_msg的md5 唯一键
    private String tnMsg; // v3:AAAAAZkX6oqE6p7G7/NU65SJPZkOrKnDeWIM4D2MI0fEGehyFBLBUHPPTY6MyEyoVRWxaFeAHgeBrAWrmrlrle12zOjDcDzXthuF960mYnWlQbm9ZbalNNV0IiaZi2HYvOsUMTHr7fDKUph03yFIkBQEAzHM0uGEii8Z8xFM1eRevHNbhgRMv5nJunueV92Scz9kRLCVFSN5ssuNrWXIEGCmXenau1+BqtMdKu5LlzPE2zqXXP1D/PC23ly5MsPeewb0dgzz2F1VXoBEPAj4jnhGZo4Io54fj1oL6CIwpgwj4s/7cbPwbf8uGKtlrtp+hrzUNh6QXWI6l3U4xBxkBznqfGxvti+YM+qkKZwxOOg6U4zshI2ROEH+N9UoQFbNWZRQMQZeYDuQ3dHYuA/SVAFAY0diYsFkKnIytjfyZthroB0atPrNWoOdE0i4tFH+alLyK/y6AlFbhBb221iM1RsyaHjNrpyPjh2UGmwgFkCvpU1LrR2P/Y0WLmunhAtsJ3dLVK+E/RCT12Z+Rw==
    private String tnRespondsData; // JSONObject.toJSONString(TnRespondsData)
    private Integer ctime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getTnMsgMd5() {
        return tnMsgMd5;
    }

    public void setTnMsgMd5(String tnMsgMd5) {
        this.tnMsgMd5 = tnMsgMd5;
    }

    public String getTnMsg() {
        return tnMsg;
    }

    public void setTnMsg(String tnMsg) {
        this.tnMsg = tnMsg;
    }

    public String getTnRespondsData() {
        return tnRespondsData;
    }

    public void setTnRespondsData(String tnRespondsData) {
        this.tnRespondsData = tnRespondsData;
    }

    public Integer getCtime() {
        return ctime;
    }

    public void setCtime(Integer ctime) {
        this.ctime = ctime;
    }
}
