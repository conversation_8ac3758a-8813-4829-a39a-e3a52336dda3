package com.quhong.redis;

import com.quhong.config.ConfigOrder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;
import org.springframework.data.redis.core.StringRedisTemplate;

@Configuration
@Order(ConfigOrder.REDIS_CONFIG)
public class RedisBean extends BaseRedisBean {
    private static final Logger logger = LoggerFactory.getLogger(RedisBean.class);

    public static final String LOCK = "redis_lock_bean";
    public static final String IM = "redis_im_bean";
    public static final String IM_LOG = "redis_im_log_bean";
    public static final String ROOM = "redis_room_bean";
    public static final String ACTOR = "redis_actor_bean";
    public static final String TOKEN = "redis_token_Bean";
    public static final String STATUS = "redis_status_Bean";


    private static final String IM_PRE = "im";
    private static final String LOCK_PRE = "dis-lock";
    private static final String TOKEN_PRE = "token";
    private static final String STATUS_PRE = "status";

    @Value("${redis.password}")
    private String password;

    public RedisBean() {

    }

    @Bean(name = LOCK) // redis1 172.31.39.116:6389 db 0
    public StringRedisTemplate getLockBean() {
        return createBean(LOCK_PRE, null);
    }

    @Bean(name = IM) // redis2 172.31.7.70:7382 db 5
    public StringRedisTemplate getIMMainBean() {
        return createBean(IM_PRE, null);
    }

    @Bean(name = IM_LOG) // redis2 172.31.7.70:7382 db 6
    public StringRedisTemplate getIMLogBean() {
        return createBean(IM_PRE, "log");
    }

    @Bean(name = ROOM) // redis2 172.31.7.70:7382 db 7
    public StringRedisTemplate getRoomBean() {
        return createBean(IM_PRE, "room");
    }

    @Bean(name = ACTOR) // redis2 172.31.7.70:6385 172.31.7.70:6386 172.31.7.70:6387
    public StringRedisTemplate getActorBean() {
        return createBean("actor", null);
    }

    @Bean(name = TOKEN) // redis3 172.31.42.20 6385 db 2
    public StringRedisTemplate getTokenBean() {
        return createBean(TOKEN_PRE, null);
    }

    @Bean(name = STATUS) // redis2 172.31.7.70:7382 db 9
    public StringRedisTemplate getStatusBean() {
        return createBean(IM_PRE, STATUS_PRE);
    }
}
