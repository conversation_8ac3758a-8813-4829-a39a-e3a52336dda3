package com.quhong.service.impl;

import com.quhong.constant.LoginConstant;
import com.quhong.constant.LoginHttpCode;
import com.quhong.core.concurrency.BaseTaskFactory;
import com.quhong.core.concurrency.tasks.Task;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.*;
import com.quhong.data.dto.BeforeLoginDTO;
import com.quhong.data.dto.CheckOutPhoneDTO;
import com.quhong.data.vo.AccountBlockRspVO;
import com.quhong.data.vo.BeforeLoginVO;
import com.quhong.enums.ClientOS;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonException;
import com.quhong.mongo.dao.PhoneAccountDao;
import com.quhong.mongo.data.PhoneAccountData;
import com.quhong.redis.ThirdApiCallRedis;
import com.quhong.redis.TnCheckRedis;
import com.quhong.service.AbstractLoginService;
import com.quhong.service.ThirdApiLoginService;
import com.quhong.utils.AppVersionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.regex.Pattern;

@Service
public class FireBasePhoneLoginService extends AbstractLoginService {
    private static final Logger logger = LoggerFactory.getLogger(FireBasePhoneLoginService.class);
    private static final Pattern phonePattern = Pattern.compile("^\\+\\d{9,20}$");
    private static final int MAX_SEND_SMS_COUNT = 3;
    private static final List<Long> SEND_SMS_LIMIT_REQUEST_COUNT = Arrays.asList(20000L, 40000L);

    @Resource
    private PhoneAccountDao phoneAccountDao;
    @Resource
    private TnCheckRedis tnCheckRedis;
    @Resource
    private ThirdApiCallRedis thirdApiCallRedis;

    @Override
    protected void initData(RegisterOrLoginContext context) {
        super.initData(context);
        ValidPhoneAccountData validPhoneAccountData = getValidPhoneAccount(context.getAccount(),
                context.getpCountryCode(), context.getpNumber());
        if (validPhoneAccountData == null) {
            logger.info("account is empty context={}", context);
            throw new CommonException(LoginHttpCode.PARAM_ERROR);
        } else {
            String oldAccount = validPhoneAccountData.getOldAccount();
            String newAccount = validPhoneAccountData.getNewAccount();
            String newAccountByZero = validPhoneAccountData.getNewAccountByZero();
            if (!StringUtils.isEmpty(oldAccount)) {
                context.setAccount(oldAccount);
            } else {
                context.setAccount(newAccount);
            }
            context.setDbPhoneAccount(newAccount);
            context.setDbPhoneAccountZero(newAccountByZero);
            context.setValidAccount(validPhoneAccountData.isValidAccount());
        }
    }

    @Override
    public RegisterOrLoginContext beforeLogin(RegisterOrLoginContext context) {
        String account = context.getAccount();
        String newAccount = context.getDbPhoneAccount();
        String newAccountZero = context.getDbPhoneAccountZero();
//        account = account.trim().replace(" ", "");
//        String arbAccount = handleArbNum(account); //  新版的电话号码才会转，所以这里要兼容下
//        context.setDbPhoneAccount(account);
//        context.setDbPhoneAccountZero(arbAccount);
        String pwd = context.getPassword();
        if (!StringUtils.isEmpty(context.getLoginToken())) { //  注册或者修改密码
            if (account.length() > 32) {
                logger.info("account length error context={} ", context);
                loginDataCountService.recordLoginStatusToTga(context, LoginConstant.LOGIN_STATE_SEVER_PARM_ERROR);
                throw new CommonException(LoginHttpCode.PHONE_LOGIN_ACCOUNT_ERROR);
            }
            int newPwdLength = pwd.length();
            if (newPwdLength > 12 || newPwdLength < 6) {
                logger.info("pwd length error context={} ", context);
                loginDataCountService.recordLoginStatusToTga(context, LoginConstant.LOGIN_STATE_SEVER_PARM_ERROR);
                throw new CommonException(LoginHttpCode.SPECIAL_GUST_SET_FORMAT);
            }

            if (context.getLoginToken().length() == 32) {
                logger.info("phone register or change pwd length is 32 invalid token context={}", context);
                monitorSender.info(ThirdApiLoginService.LOGIN_WARN_NAME, "手机号注册或修改密码，客户端上报的第三方token不合法",
                        "context=" + context);
            } else {
                FireBaseLoginVerifyData data = thirdApiLoginService.getFireBase(context);
                if (data.getUser_id() != null) {
                    context.setThirdUid(data.getUser_id());
                    context.setRealPhoneNum(StringUtils.isEmpty(data.getPhone_number()) ? "" : data.getPhone_number());
                    context.setCheckPhoneAccount(false);
                    getExistPhoneAccount(context);
                    return context;
                }
            }
        }
        PhoneAccountData phoneAccountData = getExistPhoneAccount(context);
        if (null == phoneAccountData) {
            logger.info("not find phoneAccount context={} ", context);
            loginDataCountService.recordLoginStatusToTga(context, LoginConstant.LOGIN_STATE_SEVER_PARM_ERROR);
            throw new CommonException(LoginHttpCode.PHONE_LOGIN_NOT_FIND);
        } else {
            if (!phoneAccountData.getPwd().equals(pwd)) {
                logger.info("phoneAccount pwd error context={} ", context);
                loginDataCountService.recordLoginStatusToTga(context, LoginConstant.LOGIN_STATE_SEVER_PARM_ERROR);
                throw new CommonException(LoginHttpCode.PHONE_LOGIN_PWD_ERROR);
            } else {
                context.setThirdUid(phoneAccountData.getUid());
                context.setCheckPhoneAccount(true);
            }
        }
        return context;
    }

    public PhoneAccountData getExistPhoneAccount(RegisterOrLoginContext context) {
        String account = context.getAccount();
        String newAccountZero = context.getDbPhoneAccountZero();
        boolean isMayBeTwoRecord = true;
        PhoneAccountData phoneAccountData = phoneAccountDao.findDataByAccount(account);
        if (null == phoneAccountData && !StringUtils.isEmpty(newAccountZero)) {
            phoneAccountData = phoneAccountDao.findDataByAccount(newAccountZero);
            isMayBeTwoRecord = false;
        }
        context.setExistPhoneAccount(phoneAccountData);
        context.setMayBeTwoRecord(isMayBeTwoRecord);
        return phoneAccountData;
    }

    @Override
    public RegisterOrLoginContext afterLogin(RegisterOrLoginContext context) {
        String account = context.getAccount();
        String newAccount = context.getDbPhoneAccount();
        String newAccountZero = context.getDbPhoneAccountZero();

        if (!context.isCheckPhoneAccount()) { //  注册或者修改密码
            boolean isMayBeTwoRecord = context.isMayBeTwoRecord();
            PhoneAccountData phoneAccountData = context.getExistPhoneAccount();
            if (null == phoneAccountData) {
                boolean isSameNumber = isSameNumber(account, context.getRealPhoneNum());
                if (context.isRegister()) { // 注册
                    if (isSameNumber && context.isValidAccount()) {
                        phoneAccountDao.insertData(account, context.getUid(), context.getPassword()
                                , context.getRealPhoneNum(), context.getThirdUid());
                    }
                    logger.info("register PhoneAccount success account:{},uid:{} realPhoneNum:{} thirdUid:{} isSameNumber:{}" +
                                    " isValidAccount:{} loginType:{}",
                            account, context.getUid(), context.getRealPhoneNum(), context.getThirdUid(), isSameNumber,
                            context.isValidAccount(), context.getType());
                } else { // 修改密码,可能是换号造成的数据错乱，actor表有记录，但phoneAccount没有
                    phoneAccountData = phoneAccountDao.findData(context.getUid());
                    if (null != phoneAccountData) { // 为了避免一个uid跟多个account关联，造成后面数据混乱，这里限制
                        String oldAccount = phoneAccountData.get_id();
                        logger.info("try insert PhoneAccount again fail account:{},uid:{},old_account:{} realPhoneNum:{} " +
                                        "thirdUid:{} loginType:{}",
                                account, context.getUid(), oldAccount, context.getRealPhoneNum(),
                                context.getThirdUid(), context.getType());
                        String msg = String.format("login fail, phone:%s is invalid because old phone:%s already register",
                                account, oldAccount);
                        loginDataCountService.recordLoginStatusToTga(context, LoginConstant.LOGIN_STATE_SEVER_PARM_ERROR);
                        throw new CommonException(new HttpCode(LoginHttpCode.LOGIN_FAIL.getCode(), msg));
                    } else {
                        // 除了注册逻辑判断没写，其他可能是换号造成的数据错乱已经关联不上了，或者脚本删除了记录
                        if (isSameNumber && context.isValidAccount()) {
                            phoneAccountDao.insertData(account, context.getUid(), context.getPassword()
                                    , context.getRealPhoneNum(), context.getThirdUid());
                        }
                        logger.info("login insert PhoneAccount success account:{},uid:{} realPhoneNum:{} thirdUid:{} isSameNumber:{}" +
                                        " isValidAccount:{} loginType:{}",
                                account, context.getUid(), context.getRealPhoneNum(), context.getThirdUid(), isSameNumber,
                                context.isValidAccount(), context.getType());
                    }
                }
            } else {// 修改密码
                boolean isSameUid = context.getUid().equals(phoneAccountData.getUid());
                int changePwdReason;
                // 不是同一个用户
                if (!isSameUid) {
                    // 比较手机号后7位
                    String a = getLastPosStr(account, 7);
                    String b = getLastPosStr(context.getRealPhoneNum(), 7);
                    if (StringUtils.isEmpty(a) || StringUtils.isEmpty(b)) {
                        changePwdReason = 1;
                    } else {
                        if (a.equals(b)) {
                            changePwdReason = 2;
                        } else {
                            changePwdReason = 0;
                        }
                    }
                } else {
                    if (!phoneAccountData.getPwd().equals(context.getPassword())) {
                        changePwdReason = 3;
                    } else {
                        changePwdReason = -1;
                    }
                }

                if (changePwdReason > 0) {
                    phoneAccountDao.updatePwd(phoneAccountData.get_id(), context.getUid(), context.getPassword());
                    logger.info("update PhoneAccount password success account:{},uid:{},old_uid:{},is_same:{} changePwdReason:{}" +
                                    " realPhoneNum:{} thirdUid:{} loginType:{}",
                            phoneAccountData.get_id(), context.getUid(), phoneAccountData.getUid(), isSameUid, changePwdReason,
                            context.getRealPhoneNum(), context.getThirdUid(), context.getType());
                } else {
                    // 可能重复提交了
                    logger.info("update PhoneAccount password fail account:{},uid:{},old_uid:{},is_same:{} changePwdReason:{}" +
                                    " realPhoneNum:{} thirdUid:{} loginType:{}",
                            phoneAccountData.get_id(), context.getUid(), phoneAccountData.getUid(), isSameUid, changePwdReason,
                            context.getRealPhoneNum(), context.getThirdUid(), context.getType());
                }

                if (isMayBeTwoRecord && !StringUtils.isEmpty(newAccountZero)) {
                    // 同时修改带0账号的uid,密码
                    PhoneAccountData zeroPhoneAccountData = phoneAccountDao.findDataByAccount(newAccountZero);
                    if (zeroPhoneAccountData != null) {
                        boolean twoAccountIsSameUid = phoneAccountData.getUid().equals(zeroPhoneAccountData.getUid());
                        //  两个账号的uid不一致，可能是原来换号造成的差异
                        if (changePwdReason > 0 && twoAccountIsSameUid) {
                            phoneAccountDao.updatePwd(zeroPhoneAccountData.get_id(), context.getUid(), context.getPassword());
                        }
                        logger.info("pass update zeroPhoneAccountData password oneAccount:{},twoAccount:{},twoAccountIsSameUid:{}," +
                                        "actorUid:{},oneUid:{},twoUid:{},isSameActorUid:{} changePwdReason:{}" +
                                        " realPhoneNum:{} thirdUid:{} loginType:{}",
                                phoneAccountData.get_id(), zeroPhoneAccountData.get_id(), twoAccountIsSameUid,
                                context.getUid(), phoneAccountData.getUid(), zeroPhoneAccountData.getUid(), isSameUid, changePwdReason,
                                context.getRealPhoneNum(), context.getThirdUid(), context.getType());
                    }
                }

            }

        } else { //  登入
            logger.info("login phoneAccount success reqId={} uid={} account={} newAccount={} newAccountZero={} loginType={}",
                    context.getRequestId(), context.getUid(), account, newAccount, newAccountZero, context.getType());
        }
        return context;
    }

    private boolean isSameNumber(String aAccount, String bAccount) {
        String a = getLastPosStr(aAccount, 7);
        String b = getLastPosStr(bAccount, 7);
        if (StringUtils.isEmpty(a) || StringUtils.isEmpty(b)) {
            return true;
        } else {
            return a.equals(b);
        }
    }

    private String getLastPosStr(String src, int lastPos) {
        try {
            if (StringUtils.isEmpty(src)) {
                return null;
            }
            int len = src.length();
            if (lastPos > len) {
                return null;
            } else {
                return src.substring(len - lastPos, len);
            }
        } catch (Exception e) {
            logger.info("src:{} lastPos:{} error msg:{}", src, lastPos, e.getMessage());
            return null;
        }
    }

    public BeforeLoginVO checkPhone(CheckOutPhoneDTO dto) {
        if (dto.getNew_versioncode() < 5) {
            throw new CommonException(LoginHttpCode.UPDATE_YOUR_APP);
        }

        ValidPhoneAccountData validPhoneAccountData = getValidPhoneAccount(dto.getAccount(), dto.getpCountryCode(), dto.getpNumber());
        if (validPhoneAccountData == null) {
            logger.info("params error dto={}", dto);
            throw new CommonException(LoginHttpCode.PARAM_ERROR);
        } else {
            String oldAccount = validPhoneAccountData.getOldAccount();
            String newAccount = validPhoneAccountData.getNewAccount();
            String newAccountByZero = validPhoneAccountData.getNewAccountByZero();

            String phoneAccount = newAccount;
            Set<String> accountSet = new HashSet<>();
            if (!StringUtils.isEmpty(oldAccount)) {
                accountSet.add(oldAccount);
                phoneAccount = oldAccount;
            } else {
                accountSet.add(newAccount);
                accountSet.add(newAccountByZero);
            }

            List<PhoneAccountData> list = phoneAccountDao.findPhoneByAccount(accountSet);
            if (CollectionUtils.isEmpty(list)) {
                if (isLimitRegister(dto)) {
                    throw new CommonException(LoginHttpCode.PHONE_LOGIN_NOT_REGISTER);
                }
                return checkSendNum(dto, phoneAccount);
            } else {
                PhoneAccountData phoneAccountData = list.get(0);
                String uid = phoneAccountData.getUid();
                ActorData actorData = actorDao.getActorData(uid);
                if (actorData == null) {
                    logger.error("not find actor phoneAccountData={}", phoneAccountData);
                    if (isLimitRegister(dto)) {
                        throw new CommonException(LoginHttpCode.PHONE_LOGIN_NOT_REGISTER);
                    }
                    return checkSendNum(dto, phoneAccount);
                }
                if (actorData.getIs_delete() == 1) {
                    if (isLimitRegister(dto)) {
                        throw new CommonException(LoginHttpCode.PHONE_LOGIN_NOT_REGISTER);
                    }
                    return checkSendNum(dto, phoneAccount);
                }
                if (actorData.getValid() == 0) {
                    String showMsg = actorData.getRid() + "";
                    CommonException e = new CommonException(LoginHttpCode.ACCOUNT_LOGIN_BLOCK_BAN, showMsg);
                    AccountBlockRspVO vo = getAccountBlockRspVO(uid, actorData.getRid(), dto.getSlang());
                    e.setData(vo);
                    throw e;
                }
                CommonException e = new CommonException(LoginHttpCode.PHONE_ACCOUNT_CHECK_EXISTED);
                BeforeLoginVO vo = checkSendNum(dto, phoneAccount);
                e.setData(vo);
                throw e;
            }
        }
    }

    private boolean isLimitRegister(CheckOutPhoneDTO dto) {
        return dto.getOs() == ClientOS.ANDROID;
//        return dto.getOs() == ClientOS.ANDROID&& AppVersionUtils.versionCheck(850, dto);
    }


    public ValidPhoneAccountData getValidPhoneAccount(String account, String code, String number) {
        ValidPhoneAccountData validPhoneAccountData;
        if (StringUtils.isEmpty(code) || StringUtils.isEmpty(number)) {
            if (StringUtils.isEmpty(account)) {
                return null;
            } else {
                // 老包的请求
                account = account.trim().replace(" ", "");
                validPhoneAccountData = new ValidPhoneAccountData();
                String rAccount = handleArbNum(account);
                validPhoneAccountData.setOldAccount(rAccount); //  阿语转为纯数字的
                validPhoneAccountData.setValidAccount(phonePattern.matcher(rAccount).matches());
                return validPhoneAccountData;
            }
        } else {
            // 8.37及以后的请求
            code = handleArbNum(code.trim().replace(" ", ""));
            number = handleArbNum(number.trim().replace(" ", "").replaceFirst("^0*", ""));
            validPhoneAccountData = new ValidPhoneAccountData();
            String rAccount = code + number;
            validPhoneAccountData.setNewAccount(rAccount);
            validPhoneAccountData.setValidAccount(phonePattern.matcher(rAccount).matches());
            validPhoneAccountData.setNewAccountByZero(code + "0" + number);
            return validPhoneAccountData;
        }
    }

    public BeforeLoginVO checkSendNum(CheckOutPhoneDTO dto, String phoneAccount) {
        BeforeLoginVO vo = new BeforeLoginVO();
        if (dto.getFromType() == 1) {
            return vo;
        }

        if (!AppVersionUtils.versionCheck(850, dto)) {
            logger.error("update app dto={} phoneAccount:{}", dto, phoneAccount);
            throw new CommonException(LoginHttpCode.UPDATE_YOUR_APP);
        }
        String tnId = dto.getOpenId();
        if (ObjectUtils.isEmpty(tnId) && !ObjectUtils.isEmpty(dto.getTnMsg())) {
            RegisterOrLoginContext ctx = new RegisterOrLoginContext();
            ctx.setIp(dto.getIp());
            ctx.setOs(dto.getOs());
            ctx.setTn_msg(dto.getTnMsg());
            ctx.setDbPhoneAccount(phoneAccount);
//            TnRespondsData tnData = thirdApiLoginService.getTnDeviceData(ctx);
            TnRespondsData tnData = thirdApiLoginService.getTnDeviceDataNew(ctx);
            if (tnData != null) {
                tnId = tnData.getOpenid();
            }
            ctx.setTnId(tnId);
            ctx.setDistinct_id("check_phone_" + ctx.getRequestId());
            loginDataCountService.tnCheckTask(ctx);
        }
        if (ObjectUtils.isEmpty(tnId)) {
            logger.error("tnId is empty dto={}", dto);
            throw new CommonException(LoginHttpCode.TN_MSG_EMPTY);
        } else {
            vo.setOpenId(tnId);
            int num = tnCheckRedis.geTnDaySmsData(tnId, DateHelper.ARABIAN.formatDateInDay());
            vo.setRemainingSendNum(Math.max(0, MAX_SEND_SMS_COUNT - num));
            return vo;
        }
    }


    public BeforeLoginVO incSendNum(BeforeLoginDTO dto) {
        String openId = dto.getOpenId();
        if (ObjectUtils.isEmpty(openId)) {
            logger.error("openId is empty dto={}", dto);
            throw new CommonException(LoginHttpCode.PARAM_ERROR);
        }
        int nowNum = tnCheckRedis.geTnDaySmsData(openId, DateHelper.ARABIAN.formatDateInDay());
        if (nowNum >= MAX_SEND_SMS_COUNT) {
            logger.info("openId={} gte max nowNum={}>{}", openId, nowNum, MAX_SEND_SMS_COUNT);
            throw new CommonException(LoginHttpCode.LOGIN_SEND_SMS_LIMIT);
        }
        BeforeLoginVO vo = new BeforeLoginVO();
        vo.setOpenId(openId);
        int num = (int) tnCheckRedis.incrementTnDaySmsData(openId, DateHelper.ARABIAN.formatDateInDay(), 1);
        vo.setRemainingSendNum(Math.max(0, MAX_SEND_SMS_COUNT - num));
        sendSmsCheckTask(dto);
        return vo;
    }

    public void sendSmsCheckTask(BeforeLoginDTO dto) {
        BaseTaskFactory.getFactory().addSlow(new Task() {
            @Override
            protected void execute() {
                try {
                    long curValue = thirdApiCallRedis.incApiCallNum(ThirdApiCallRedis.FIREBASE_SEND_SMS);
                    if (SEND_SMS_LIMIT_REQUEST_COUNT.contains(curValue)) {
                        logger.info("达到当日firebase短信请求告警限制 curValue={} ", curValue);
                        monitorSender.info(LoginConstant.USTAR_JAVA_EXCEPTION_WARN_NAME, "达到当日firebase短信请求告警限制",
                                "curValue=" + curValue);
                    }
                    logger.info("sendSmsCheckTask success uid:{} tn_id:{} curValue:{}", dto.getUid(), dto.getOpenId(), curValue);
                } catch (Exception e) {
                    logger.error("sendSmsCheckTask error, dto={} error msg={}", dto, e.getMessage(), e);
                }
            }
        });

    }
}
