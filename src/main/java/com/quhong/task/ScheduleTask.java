package com.quhong.task;


import com.quhong.config.AsyncConfig;
import com.quhong.mongo.config.MongoBean;
import com.quhong.mongo.dao.CommonDao;
import com.quhong.mongo.data.MongoLoginActorData;
import com.quhong.service.HandleRiskService;
import com.quhong.service.impl.RobotLoginService;
import com.quhong.utils.K8sUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

@Component
public class ScheduleTask {
    private static final Logger logger = LoggerFactory.getLogger(ScheduleTask.class);
    @Resource
    private K8sUtils k8sUtils;

    @Resource
    private RobotLoginService robotLoginService;

    @Resource
    private HandleRiskService handleRiskService;

    @Resource
    private CommonDao commonDao;

    @Resource(name = MongoBean.MOVIES)
    private MongoTemplate mongoTemplate;

    /**
     * 每5分钟的第55秒执行  55 0/5 * * * ?
     */
    @Async(AsyncConfig.ASYNC_TASK)
    @Scheduled(cron = "55 0/5 * * * ?")
    public void genRobotAccount() {
        if (k8sUtils.isMasterFromCache()) {
            robotLoginService.threadGenRobotAccount();
        }
    }


    /**
     * 每天上午2点15触发
     */
//    @Async(AsyncConfig.ASYNC_TASK)
//    @Scheduled(cron = "0 15 02 * * ?")
//    public void handleCreditRiskByDay() {
//        if (k8sUtils.isMasterFromCache()) {
//            handleRiskService.handleCreditRiskByDay();
//        }
//    }

    /**
     * 每个小时的45分钟
     */
//    @Async(AsyncConfig.ASYNC_TASK)
//    @Scheduled(cron = "0 55 0/1 * * ?")
//    public void handleCreditRiskByHour() {
//        if (k8sUtils.isMasterFromCache()) {
//            handleRiskService.handleCreditRiskByHour();
//        }
//    }


    public void findAllIos() {
        try {
            Criteria criteria = Criteria.where("login_type").is(5);
            List<MongoLoginActorData> allIos =  commonDao.findList(new Query(criteria), MongoLoginActorData.class);
            List<Integer> hideList= Arrays.asList(5671568,6976247,7014214,19,6507351,7013882,188888,7013989,90002,5631150
            ,11113,7014361,6657716,7013972,6583774,7013884,7014981,550066,666777,7015141,6643842,7014026,7015202,101011
            ,7013835,911911,5000454,6394336,5123565,4506345,5873075,7014822,6354373,7013958,3944092,5378775);
            for (MongoLoginActorData item:allIos){
                int rid = item.getRid();
                if (hideList.contains(rid)){
                    continue;
                }
                long registerTime = item.get_id().getTimestamp() * 1000L;
                if (registerTime <= 1702267200000L){
                    Query query = new Query(Criteria.where("_id").is(item.get_id()));
                    Update update = new Update();
                    update.set("back_uid", item.getUid());
                    mongoTemplate.updateFirst(query, update, "actor");
                }

            }

        } catch (Exception e) {
            logger.error("findAllIos={} {}", e.getMessage(), e);
        }
    }


}
