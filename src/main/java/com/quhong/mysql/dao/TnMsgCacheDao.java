package com.quhong.mysql.dao;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.quhong.config.CaffeineCacheConfig;
import com.quhong.data.TnRespondsData;
import com.quhong.mysql.data.RobotActorData;
import com.quhong.mysql.data.TnMsgCacheData;
import com.quhong.mysql.mapper.ustar.RobotActorMapper;
import com.quhong.mysql.mapper.ustar.TnMsgCacheMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.List;

@Component
public class TnMsgCacheDao {
    private static final Logger logger = LoggerFactory.getLogger(TnMsgCacheDao.class);

    @Resource
    private TnMsgCacheMapper tnMsgCacheMapper;

    public void add(TnMsgCacheData data) {
        try {
            tnMsgCacheMapper.insert(data);
        } catch (Exception e) {
            logger.error("add msg={}", e.getMessage(), e);
        }
    }

    public int deleteByCtime(long endTime) {
        QueryWrapper<TnMsgCacheData> queryWrapper = new QueryWrapper<>();
        queryWrapper.lt("ctime", endTime);
        return tnMsgCacheMapper.delete(queryWrapper);
    }

    @Cacheable(value = "cache", cacheManager = CaffeineCacheConfig.EXPIRE_10M_AFTER_WRITE,
            key = "#root.method.name+T(org.springframework.util.StringUtils).arrayToDelimitedString(args,'-')")
    public TnRespondsData getTnRespondsDataCache(String tnMsgMd5) {
        TnMsgCacheData data = getOneByTnMsgMd5(tnMsgMd5);
        if (data == null || ObjectUtils.isEmpty(data.getTnRespondsData())) {
            return null;
        }
        try {
            return JSON.parseObject(data.getTnRespondsData(), TnRespondsData.class);
        } catch (Exception e) {
            logger.error("getTnRespondsDataCache error. {}", e.getMessage(), e);
            return null;
        }
    }

    public TnMsgCacheData getOneByTnMsgMd5(String tnMsgMd5) {
        QueryWrapper<TnMsgCacheData> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("tn_msg_md5", tnMsgMd5);
        return tnMsgCacheMapper.selectOne(queryWrapper);
    }
}
