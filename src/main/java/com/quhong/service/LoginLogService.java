package com.quhong.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.quhong.mysql.data.LoginLogData;
import com.quhong.mysql.mapper.ustar_log.LoginLogMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

@Service
public class LoginLogService extends ServiceImpl<LoginLogMapper, LoginLogData> {
    private static final Logger logger = LoggerFactory.getLogger(LoginLogService.class);

    public void insertOne(LoginLogData loginLogData) {
        try {
            save(loginLogData);
        } catch (Exception e) {
            logger.error("insertOne msg={}", e.getMessage(), e);
        }
    }

}
