package com.quhong.task;

import com.quhong.constant.AccountConstant;
import com.quhong.core.utils.DateHelper;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mysql.data.AccountStatusData;
import com.quhong.service.mysql.AccountStatusService;
import com.quhong.utils.CollectionUtil;
import com.quhong.utils.K8sUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;

@Component
public class DeleteAccountTask {
    private final static Logger logger = LoggerFactory.getLogger(DeleteAccountTask.class);
    @Resource
    private ActorDao actorDao;
    @Resource
    private K8sUtils k8sUtils;
    @Resource
    private AccountStatusService accountStatusService;

    /**
     * 每天01:00执行删除30天前请求删除账号的用户（北京时间上午9点）
     */
    @Scheduled(cron = "0 0 1 * * ?")
    private void deleteAccount() {
        if (!k8sUtils.isMaster()) {
            return;
        }
        long currentTimeMillis = System.currentTimeMillis();
        int deleteTime = DateHelper.getNowSeconds() - 30 * 24 * 60 * 60;
        List<AccountStatusData> accountToDelete = accountStatusService.lambdaQuery()
                .eq(AccountStatusData::getStatus, AccountConstant.DELETE_FLAG)
                .eq(AccountStatusData::getPy_status, 1)
                .lt(AccountStatusData::getApplyTime, deleteTime)
                .list();
        Set<String> deleteAccountSet = CollectionUtil.listToPropertySet(accountToDelete, AccountStatusData::getUid);
        logger.info("delete account size={}", deleteAccountSet.size());
        for (String uid : deleteAccountSet) {
            try {
                doDeleteAccount(uid);
            } catch (Exception e) {
                logger.error("delete account fail. uid={} {}", uid, e.getMessage());
            }
        }
        logger.info("delete account finish. cost={}", System.currentTimeMillis() - currentTimeMillis);
    }

    /**
     * 执行用户逻辑删除
     * d. 隐藏聊天记录、配对
     */
    public void doDeleteAccount(String uid) {
        logger.info("do delete account uid={}", uid);
        if (actorDao.updateAccountStatus(uid, AccountConstant.DELETED)) {
            accountStatusService.lambdaUpdate()
                    .eq(AccountStatusData::getUid, uid)
                    .set(AccountStatusData::getStatus, AccountConstant.DELETED)
                    .set(AccountStatusData::getMtime, DateHelper.getNowSeconds())
                    .update();
            logger.info("delete account success uid={}", uid);
        } else {
            logger.error("delete account fail. uid={}", uid);
        }
    }
}
