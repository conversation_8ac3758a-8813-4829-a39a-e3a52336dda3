package com.quhong.config;

import com.quhong.redis.BaseRedisBean;
import com.quhong.room.RoomMessageListener;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.listener.PatternTopic;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;
import org.springframework.data.redis.listener.adapter.MessageListenerAdapter;

@Configuration
public class RedisSubscriptionConfig {
    public static final String MESSAGE = "redis_message_Bean";

    private static final String MESSAGE_PRE = "message";// redis2 *********** 7382 db0

    public static final String IM_MESSAGE = "im_message*";
//    public static final String IM_MESSAGE = "im_message:monitor_warn_user";

    private WebRedisBean webRedisBean;

    public RedisSubscriptionConfig() {
        webRedisBean = new WebRedisBean();
    }

    @Bean
    RedisMessageListenerContainer container() {
        RedisMessageListenerContainer container = new RedisMessageListenerContainer();
        StringRedisTemplate redisTemplate = getMessageBean();
        container.setConnectionFactory(redisTemplate.getConnectionFactory());
        container.addMessageListener(getRoomListener(), new PatternTopic(IM_MESSAGE));
        return container;
    }

    /**
     * 消息监听器适配器，绑定消息处理器
     *
     * @return
     */
    @Bean
    MessageListenerAdapter getRoomListener() {
        return new MessageListenerAdapter(new RoomMessageListener());
    }

    @Bean(name = MESSAGE)
    public StringRedisTemplate getMessageBean() {
        return webRedisBean.createBean(MESSAGE_PRE, null);
    }

    public static class WebRedisBean extends BaseRedisBean {
        @Override
        public StringRedisTemplate createBean(String pre, String dbPre) {
            return super.createBean(pre, dbPre);
        }
    }
}

